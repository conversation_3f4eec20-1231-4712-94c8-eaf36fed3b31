//
//  NewWebBrowserView.swift
//  cop
//
//  Created by Augment Agent on 2025/6/2.
//

import SwiftUI
import WebKit

// MARK: - 简化的导航目标枚举
enum BrowserNavigationDestination: Hashable {
    case history
    case settings
    case userAgent
    case searchEngine
    case security
    case dataManagement
    case permissions
    case statistics
}

// MARK: - 新的浏览器主视图
struct NewWebBrowserView: View {
    @EnvironmentObject var browserViewModel: NewWebBrowserViewModel
    @StateObject private var serviceManager = ServiceManager.shared
    @State private var isEditingAddress = false
    @State private var addressText = ""
    @State private var showingTabSwitcher = false
    @State private var showingBookmarks = false
    @State private var showingAddressSuggestions = false
    @State private var addressSuggestions: [AddressSuggestion] = []
    @FocusState private var isAddressFieldFocused: Bool
    
    // NavigationStack 路径管理 - 修复：使用@State而不是ObservableObject的@Published
    @State private var navigationPath = NavigationPath()
    
    // 防止NavigationPath频繁更新的去抖动器
    @State private var pathUpdateWorkItem: DispatchWorkItem?
    
    // 统一的弹窗管理（只保留必要的弹窗）
    @State private var currentSheet: BrowserSheetType? = nil
    
    // 视频全屏状态管理 - 简化处理
    @State private var fullScreenBrightness: CGFloat = UIScreen.main.brightness
    @State private var isVideoFullScreen: Bool = false

    let onToggleSidebar: (() -> Void)?

    var displayURL: String {
        if isEditingAddress {
            return addressText
        }
        if let url = browserViewModel.currentTab?.url {
            return url.absoluteString
        }
        return "输入网址或搜索"
    }

    var currentTabURL: String {
        return browserViewModel.currentTab?.url?.absoluteString ?? ""
    }
    
    var body: some View {
        NavigationStack(path: $navigationPath) {
            ZStack {
                VStack(spacing: 0) {
                    // 导航栏 - 仅在非全屏时显示
                    if !isVideoFullScreen {
                        navigationBar
                            .transition(.opacity.combined(with: .move(edge: .top)))
                        
                        // 地址栏建议列表
                        if showingAddressSuggestions && !addressSuggestions.isEmpty {
                            addressSuggestionsView
                        }
                    }
                    
                    // WebView 容器
                    webViewContainer
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                }
                
                // 退出全屏的悬浮按钮
                if isVideoFullScreen {
                    VStack {
                        HStack {
                            Spacer()
                            Button(action: exitFullScreen) {
                                Image(systemName: "xmark.circle.fill")
                                    .font(.title)
                                    .foregroundColor(.white)
                                    .background(Color.black.opacity(0.6))
                                    .clipShape(Circle())
                            }
                            .padding(.top, 50)
                            .padding(.trailing, 20)
                        }
                        Spacer()
                    }
                    .transition(.opacity)
                    .animation(.easeInOut(duration: 0.3), value: isVideoFullScreen)
                }
            }
            .background(Color(UIColor.systemBackground))
            .ignoresSafeArea(.all, edges: .top)
            .navigationBarHidden(true)
            .statusBarHidden(isVideoFullScreen)
            .animation(.easeInOut(duration: 0.3), value: isVideoFullScreen)
            .navigationDestination(for: BrowserNavigationDestination.self) { destination in
                switch destination {
                case .history:
                    NewBrowserHistoryView(browserViewModel: browserViewModel)
                case .settings:
                    OptimizedBrowserSettingsView(browserViewModel: browserViewModel, navigationPath: $navigationPath)
                case .userAgent:
                    UserAgentSelectionView(browserViewModel: browserViewModel)
                case .searchEngine:
                    SearchEngineSelectionView(browserViewModel: browserViewModel)
                case .security:
                    SecuritySettingsView(navigationPath: $navigationPath)
                case .dataManagement:
                    DataManagementView()
                case .permissions:
                    PermissionManagementView()
                case .statistics:
                    SecurityStatisticsView(statistics: SecurityService.shared.statistics)
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            // 当应用进入前台时，确保全屏状态正确
            if isVideoFullScreen {
                handleFullScreenChange(true)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("VideoDidEnterFullScreen"))) { _ in
            // 视频进入全屏
            isVideoFullScreen = true
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("VideoDidExitFullScreen"))) { _ in
            // 视频退出全屏
            isVideoFullScreen = false
        }
        .preferredColorScheme(isVideoFullScreen ? .dark : nil)
        .sheet(item: $currentSheet) { sheetType in
            switch sheetType {
            case .tabSwitcher:
                NewTabSwitcherView(browserViewModel: browserViewModel) {
                    // 新标签创建后的回调
                    resetAddressBarState()
                    updateAddressText()
                }
                .presentationDetents([.large])
                .presentationDragIndicator(.visible)
                .onDisappear {
                    // 确保在弹窗关闭时清理Focus状态
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        resetAddressBarState()
                    }
                }
            case .bookmarks:
                NewBookmarksView(browserViewModel: browserViewModel)
                    .presentationDetents([.large])
                    .presentationDragIndicator(.visible)
            }
        }
        .onAppear {
            // 初始化地址栏文本
            updateAddressText()
        }
        .onChange(of: browserViewModel.currentTabIndex) { _, newIndex in
            // 标签页切换时重置地址栏状态并更新内容
            resetAddressBarState()
            updateAddressText()
            
            // 修复：标签页切换时暂停其他标签页的视频
            handleTabSwitch(newIndex)
        }
        .onChange(of: currentTabURL) { _, _ in
            // 当前标签页URL变化时更新地址栏（仅在非编辑状态）
            if !isEditingAddress {
                updateAddressText()
            }
        }
        .onChange(of: addressText) { _, newValue in
            // 实时更新地址栏建议
            if isEditingAddress && !newValue.isEmpty {
                updateAddressSuggestions(for: newValue)
            } else {
                showingAddressSuggestions = false
            }
        }
        .onChange(of: isVideoFullScreen) { _, newValue in
            handleFullScreenChange(newValue)
        }
    }
    
    // MARK: - 导航栏
    private var navigationBar: some View {
        VStack(spacing: 8) {
            HStack(spacing: 12) {
                // 侧边栏切换按钮
                if let onToggleSidebar = onToggleSidebar {
                    Button(action: onToggleSidebar) {
                        Image(systemName: "sidebar.left")
                            .font(.system(size: 18, weight: .medium))
                            .foregroundColor(.primary)
                            .frame(width: 36, height: 36)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                
                // 后退按钮
                Button(action: {
                    browserViewModel.goBack()
                }) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(browserViewModel.currentTab?.canGoBack == true ? .primary : .gray)
                        .frame(width: 36, height: 36)
                        .background(Color.clear)
                        .contentShape(Rectangle())
                }
                .disabled(browserViewModel.currentTab?.canGoBack != true)
                .buttonStyle(PlainButtonStyle())

                // 前进按钮
                Button(action: {
                    browserViewModel.goForward()
                }) {
                    Image(systemName: "chevron.right")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(browserViewModel.currentTab?.canGoForward == true ? .primary : .gray)
                        .frame(width: 36, height: 36)
                        .background(Color.clear)
                        .contentShape(Rectangle())
                }
                .disabled(browserViewModel.currentTab?.canGoForward != true)
                .buttonStyle(PlainButtonStyle())
                
                // 地址栏
                addressBar
                
                // 刷新/停止按钮
                Button(action: {
                    if browserViewModel.currentTab?.isLoading == true {
                        browserViewModel.stopLoading()
                    } else {
                        browserViewModel.reload()
                    }
                }) {
                    Image(systemName: browserViewModel.currentTab?.isLoading == true ? "xmark" : "arrow.clockwise")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .frame(width: 36, height: 36)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
                
                // 收藏按钮
                Button(action: {
                    toggleBookmark()
                }) {
                    Image(systemName: isCurrentPageBookmarked() ? "heart.fill" : "heart")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(isCurrentPageBookmarked() ? .red : .primary)
                        .frame(width: 36, height: 36)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())

                // 标签页按钮
                Button(action: {
                    currentSheet = .tabSwitcher
                }) {
                    ZStack {
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(.primary, lineWidth: 1.5)
                            .frame(width: 20, height: 16)

                        Text("\(browserViewModel.tabs.count)")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.primary)
                    }
                    .frame(width: 36, height: 36)
                }
                .buttonStyle(PlainButtonStyle())

                // 书签按钮
                Button(action: {
                    currentSheet = .bookmarks
                }) {
                    Image(systemName: "book")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .frame(width: 36, height: 36)
                }
                .buttonStyle(PlainButtonStyle())

                // 设置按钮 - 修改为导航
                Button(action: {
                    updateNavigationPath(to: .settings)
                }) {
                    Image(systemName: "gear")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.primary)
                        .frame(width: 36, height: 36)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .padding(.top, UIApplication.shared.connectedScenes
                .compactMap { $0 as? UIWindowScene }
                .first?.windows
                .first?.safeAreaInsets.top ?? 0)
            
            // 进度条 - 增强显示逻辑
            if let tab = browserViewModel.currentTab {
                // 显示进度条：当页面正在加载或进度小于1.0时
                if tab.isLoading || tab.estimatedProgress < 1.0 {
                    VStack(spacing: 0) {
                        ProgressView(value: tab.estimatedProgress)
                            .progressViewStyle(LinearProgressViewStyle(tint: .blue))
                            
                        // 显示加载状态文本
                        if tab.isLoading {
                            HStack {
                                Text("正在加载...")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                Spacer()
                                Text("\(Int(tab.estimatedProgress * 100))%")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                    .monospacedDigit()
                            }
                            .padding(.horizontal, 16)
                            .padding(.top, 2)
                        }
                    }
                    .padding(.horizontal, 16)
                    .animation(.easeInOut(duration: 0.3), value: tab.estimatedProgress)
                    .animation(.easeInOut(duration: 0.3), value: tab.isLoading)
                }
            }
        }
        .background(Color(UIColor.systemBackground))
        .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
    }
    
    // MARK: - 地址栏
    private var addressBar: some View {
        ZStack {
            if isEditingAddress {
                TextField("输入网址或搜索", text: $addressText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(Color(UIColor.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue, lineWidth: 2)
                    )
                    .keyboardType(.webSearch)
                    .textInputAutocapitalization(.never)
                    .autocorrectionDisabled()
                    .disableAutocorrection(true)
                    .onSubmit {
                        handleAddressSubmit()
                    }
                    .onTapGesture {
                        // 确保点击时选中所有文本
                        selectAllAddressText()
                    }
                    .onAppear {
                        // 自动选中所有文本
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            selectAllAddressText()
                        }
                    }
                    .focused($isAddressFieldFocused)
            } else {
                HStack(spacing: 8) {
                    Image(systemName: browserViewModel.currentTab?.url?.scheme == "https" ? "lock.fill" : "globe")
                        .font(.caption)
                        .foregroundColor(browserViewModel.currentTab?.url?.scheme == "https" ? .green : .gray)
                    
                    Text(displayURL)
                        .font(.subheadline)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                        .truncationMode(.middle)
                    
                    Spacer()
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(Color(UIColor.systemGray6))
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .contentShape(Rectangle())
                .onTapGesture {
                    isEditingAddress = true
                    isAddressFieldFocused = true
                    // 进入编辑模式时，确保地址栏显示当前URL
                    addressText = currentTabURL
                }
            }
        }
    }
    
    // MARK: - WebView 容器
    private var webViewContainer: some View {
        ZStack {
            if let currentTab = browserViewModel.currentTab {
                if currentTab.url == nil {
                    // 新标签页空状态
                    NewTabEmptyState(browserViewModel: browserViewModel) { url in
                        browserViewModel.loadURL(url)
                        resetAddressBarState()
                        updateAddressText()
                    }
                } else {
                    WebViewContainer(tab: currentTab, browserViewModel: browserViewModel)
                        .id(currentTab.id) // 确保每个标签页有独立的 WebView
                }
            } else {
                // 没有标签页的空状态
                NoTabsEmptyState {
                    browserViewModel.createNewTab()
                    resetAddressBarState()
                    updateAddressText()
                }
            }
        }
        .onTapGesture {
            // 只有在显示地址栏建议时才响应点击关闭建议列表
            // 避免在新标签页空状态时意外激活搜索框
            if showingAddressSuggestions {
                showingAddressSuggestions = false
                isAddressFieldFocused = false
            }
        }
    }

    // MARK: - 收藏功能
    private func isCurrentPageBookmarked() -> Bool {
        guard let currentURL = browserViewModel.currentTab?.url?.absoluteString else { return false }
        return browserViewModel.isBookmarked(url: currentURL)
    }

    private func toggleBookmark() {
        guard let currentTab = browserViewModel.currentTab,
              let url = currentTab.url else { return }

        let urlString = url.absoluteString
        let title = currentTab.title

        // 防抖处理 - 防止快速重复点击
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if self.browserViewModel.isBookmarked(url: urlString) {
                // 移除书签
                if let existingBookmark = self.browserViewModel.bookmarks.first(where: { $0.url == urlString }) {
                    self.browserViewModel.removeBookmark(existingBookmark)
                }
            } else {
                // 添加书签
                self.browserViewModel.addBookmark(url: urlString, title: title)
            }
        }
    }

    // MARK: - 重置地址栏状态
    private func resetAddressBarState() {
        isEditingAddress = false
        isAddressFieldFocused = false
        showingAddressSuggestions = false
        addressSuggestions.removeAll()
    }
    
    // MARK: - 更新地址栏文本
    private func updateAddressText() {
        if let url = browserViewModel.currentTab?.url {
            addressText = url.absoluteString
        } else {
            addressText = ""
        }
    }

    private func handleAddressSubmit() {
        guard !addressText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            isEditingAddress = false
            isAddressFieldFocused = false
            return
        }

        if let url = browserViewModel.processURLInput(addressText) {
            browserViewModel.loadURL(url)
        }
        isEditingAddress = false
        isAddressFieldFocused = false
    }

    private func selectAllAddressText() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first,
               let textField = self.findTextField(in: window.rootViewController?.view) {
                textField.selectAll(nil)
            }
        }
    }

    private func findTextField(in view: UIView?) -> UITextField? {
        guard let view = view else { return nil }

        if let textField = view as? UITextField {
            return textField
        }

        for subview in view.subviews {
            if let textField = findTextField(in: subview) {
                return textField
            }
        }

        return nil
    }
    
    // MARK: - 地址栏建议处理
    private func updateAddressSuggestions(for input: String) {
        addressSuggestions = browserViewModel.getAddressBarSuggestions(for: input)
        showingAddressSuggestions = !addressSuggestions.isEmpty
    }
    
    private func selectSuggestion(_ suggestion: AddressSuggestion) {
        if let url = URL(string: suggestion.url) {
            browserViewModel.loadURL(url)
        }
        
        // 重置地址栏状态
        isEditingAddress = false
        isAddressFieldFocused = false
        showingAddressSuggestions = false
        updateAddressText()
        
        // 智能预加载相关页面
        if suggestion.type == .history || suggestion.type == .bookmark {
            if let url = URL(string: suggestion.url) {
                browserViewModel.preloadURL(url)
            }
        }
    }

    // MARK: - 地址栏建议视图
    private var addressSuggestionsView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(addressSuggestions) { suggestion in
                    AddressSuggestionRow(suggestion: suggestion) {
                        selectSuggestion(suggestion)
                    }
                    
                    if suggestion.id != addressSuggestions.last?.id {
                        Divider()
                            .padding(.leading, 52)
                    }
                }
            }
        }
        .frame(maxHeight: 200)
        .background(Color(UIColor.systemBackground))
        .overlay(
            Rectangle()
                .stroke(Color(UIColor.separator), lineWidth: 0.5)
        )
    }

    // MARK: - 全屏视频相关方法
    private func handleFullScreenChange(_ isFullScreen: Bool) {
        // 现在使用原生AVPlayerViewController处理全屏，无需手动管理系统UI
        // 只保留必要的屏幕亮度管理
        if isFullScreen {
            fullScreenBrightness = UIScreen.main.brightness
        } else {
            UIScreen.main.brightness = fullScreenBrightness
        }
    }
    
    private func exitFullScreen() {
        print("🎬 退出全屏模式")
        // 视频播放现在由各自的VideoPlayer实例管理
        // 无需全局控制
    }



    // MARK: - 处理标签页切换（暂停视频）
    private func handleTabSwitch(_ newIndex: Int) {
        guard browserViewModel.currentTab != nil else { return }
        
        // 暂停所有非活跃标签页的视频
        // 视频管理已整合到OptimizedVideoHandler中
        // 无需单独的VideoBackgroundManager调用
        
        print("🔄 [NewWebBrowserView] 标签页切换完成，视频播放已管理")
    }

    // MARK: - 安全的路径更新方法
    private func updateNavigationPath(to destination: BrowserNavigationDestination) {
        // 取消之前的更新任务
        pathUpdateWorkItem?.cancel()
        
        // 创建新的更新任务
        let workItem = DispatchWorkItem {
            // 确保在主线程上执行
            DispatchQueue.main.async {
                navigationPath.append(destination)
            }
        }
        
        pathUpdateWorkItem = workItem
        
        // 延迟执行，避免在同一帧内多次更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01, execute: workItem)
    }
}

// MARK: - 简化版 WebView 容器组件
struct WebViewContainer: UIViewRepresentable {
    let tab: NewBrowserTab
    let browserViewModel: NewWebBrowserViewModel
    
    func makeUIView(context: Context) -> WKWebView {
        print("🚀 [WebViewContainer] 为标签页 \(tab.id) 创建WebView")
        
        // 标记标签页为活跃状态
        tab.markAsActive()
        
        // 使用统一管理器获取或创建WebView
        let webView = tab.getWebView(userAgent: browserViewModel.effectiveUserAgent)
        
        // 使用更精确的键来避免冲突
        let configuredKey = "cop_webview_configured_\(tab.id.uuidString)"
        let alreadyConfigured = objc_getAssociatedObject(webView, configuredKey) as? Bool ?? false
        
        if !alreadyConfigured {
            // 只在第一次设置时注册消息处理器和代理
            print("🔧 [WebViewContainer] 首次配置WebView，注册消息处理器和代理")
            
            // 安全地添加消息处理器
            do {
                // 先尝试移除可能存在的处理器（避免重复添加）
                webView.configuration.userContentController.removeScriptMessageHandler(forName: "securityWarning")
            } catch {
                // 如果没有找到处理器，这是正常的
                print("ℹ️ [WebViewContainer] 没有找到已存在的securityWarning处理器")
            }
            
            // 添加新的消息处理器
            webView.configuration.userContentController.add(context.coordinator, name: "securityWarning")
            print("✅ [WebViewContainer] securityWarning消息处理器已添加")
            
            // 设置代理（总是设置，确保正确的代理）
            webView.navigationDelegate = context.coordinator
            webView.uiDelegate = context.coordinator
            
            // 标记为已配置
            objc_setAssociatedObject(webView, configuredKey, true, .OBJC_ASSOCIATION_RETAIN)
            
            print("✅ [WebViewContainer] WebView配置完成")
        } else {
            print("ℹ️ [WebViewContainer] WebView已配置，跳过消息处理器和代理设置")
            
            // 即使已配置，也要确保代理正确设置
            webView.navigationDelegate = context.coordinator
            webView.uiDelegate = context.coordinator
        }
        
        // 视频处理设置已整合到VideoPlayerService中
        // 不再需要单独的WebView设置
        
        // 添加观察器
        context.coordinator.addObservers(to: webView)
        
        // 如果标签页有待加载的URL，立即加载
        if let url = tab.url, webView.url != url {
            print("📄 [WebViewContainer] 立即加载URL: \(url.absoluteString)")
            SimplifiedBrowserManager.shared.loadURL(url, in: webView, for: tab)
        }
        
        print("✅ [WebViewContainer] WebView准备完成")
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        // 更新用户代理
        if webView.customUserAgent != browserViewModel.effectiveUserAgent {
            webView.customUserAgent = browserViewModel.effectiveUserAgent
        }
        
        // 视频处理器配置已简化，无需每次更新
    }
    
    func makeCoordinator() -> Coordinator {
        return Coordinator(tab: tab, browserViewModel: browserViewModel)
    }
    
    static func dismantleUIView(_ webView: WKWebView, coordinator: Coordinator) {
        print("🧹 [WebViewContainer] 开始清理WebView资源")
        
        // 清理观察器
        coordinator.removeObservers()
        
        // 检查是否配置过消息处理器（使用标签页特定的键）
        let configuredKey = "cop_webview_configured_\(coordinator.tab.id.uuidString)"
        let wasConfigured = objc_getAssociatedObject(webView, configuredKey) as? Bool ?? false
        
        if wasConfigured {
            // 安全地清理消息处理器
            Task { @MainActor in
                do {
                    webView.configuration.userContentController.removeScriptMessageHandler(forName: "securityWarning")
                    print("🧹 [WebViewContainer] 已清理 securityWarning 消息处理器")
                } catch {
                    print("ℹ️ [WebViewContainer] 清理消息处理器时出现错误: \(error)")
                }
                
                // 清除配置标记
                objc_setAssociatedObject(webView, configuredKey, nil, .OBJC_ASSOCIATION_RETAIN)
            }
        } else {
            print("ℹ️ [WebViewContainer] WebView未配置消息处理器，跳过清理")
        }
        
        print("✅ [WebViewContainer] WebView资源清理完成")
    }
}

// MARK: - WebView 协调器
class Coordinator: NSObject, WKNavigationDelegate, WKUIDelegate, WKScriptMessageHandler {
    let tab: NewBrowserTab
    let browserViewModel: NewWebBrowserViewModel
    var isObservingWebView = false
    weak var observedWebView: WKWebView?
    
    // 性能监控
    private var messageQueueWarningCount = 0
    private var lastPerformanceCheck = Date()

    init(tab: NewBrowserTab, browserViewModel: NewWebBrowserViewModel) {
        self.tab = tab
        self.browserViewModel = browserViewModel
        super.init()
        
        // 启动性能监控
        startPerformanceMonitoring()
    }

    deinit {
        // 安全地移除 KVO 观察
        removeObservers()
    }

    func addObservers(to webView: WKWebView) {
        guard !isObservingWebView else { return }

        webView.addObserver(self, forKeyPath: "canGoBack", options: [.new, .initial], context: nil)
        webView.addObserver(self, forKeyPath: "canGoForward", options: [.new, .initial], context: nil)
        webView.addObserver(self, forKeyPath: "estimatedProgress", options: [.new, .initial], context: nil)
        webView.addObserver(self, forKeyPath: "loading", options: [.new, .initial], context: nil)

        isObservingWebView = true
        observedWebView = webView
        
        // 立即更新状态
        DispatchQueue.main.async {
            self.updateTabState(from: webView)
        }
    }
    



    func removeObservers() {
        guard isObservingWebView, let webView = observedWebView else { return }

        webView.removeObserver(self, forKeyPath: "canGoBack")
        webView.removeObserver(self, forKeyPath: "canGoForward") 
        webView.removeObserver(self, forKeyPath: "estimatedProgress")
        webView.removeObserver(self, forKeyPath: "loading")

        isObservingWebView = false
        observedWebView = nil
    }
    
    private func updateTabState(from webView: WKWebView) {
        tab.updateState(
            isLoading: webView.isLoading,
            canGoBack: webView.canGoBack,
            canGoForward: webView.canGoForward,
            estimatedProgress: webView.estimatedProgress
        )
    }

    // MARK: - KVO 观察
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        guard let webView = object as? WKWebView else { return }

        DispatchQueue.main.async {
            switch keyPath {
            case "canGoBack":
                self.tab.updateState(canGoBack: webView.canGoBack)
            case "canGoForward":
                self.tab.updateState(canGoForward: webView.canGoForward)
            case "estimatedProgress":
                self.tab.updateState(estimatedProgress: webView.estimatedProgress)
            case "loading":
                self.tab.updateState(isLoading: webView.isLoading)
            default:
                super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
            }
        }
    }

    // MARK: - WKNavigationDelegate
    func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        DispatchQueue.main.async {
            self.tab.updateState(
                isLoading: true,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                estimatedProgress: 0.0
            )

            // 检测视频内容并启用特殊监控
            if let url = webView.url, self.isVideoContent(url: url) {
                self.setupVideoLoadingMonitoring(webView: webView)
            }
        }
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        let title = webView.title ?? "新标签页"
        let url = webView.url

        DispatchQueue.main.async {
            self.tab.updateState(
                title: title,
                url: url,
                isLoading: false,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                estimatedProgress: 1.0
            )

            // 添加到历史记录
            if let url = url {
                self.browserViewModel.addToHistory(url: url, title: title)
            }

            // 自动保存标签页状态
            self.browserViewModel.saveTabs()
            
            // 拍摄页面快照
            Task { @MainActor in
                await self.tab.takeSnapshot()
            }
        }
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        DispatchQueue.main.async {
            self.tab.updateState(
                isLoading: false,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                estimatedProgress: 0.0
            )
            
            // 显示错误页面
            self.handleNavigationError(error)
        }
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        DispatchQueue.main.async {
            self.tab.updateState(
                isLoading: false,
                canGoBack: webView.canGoBack,
                canGoForward: webView.canGoForward,
                estimatedProgress: 0.0
            )
            
            // 显示错误页面
            self.handleNavigationError(error)
        }
    }
    
    // MARK: - 错误处理
    private func handleNavigationError(_ error: Error) {
        let nsError = error as NSError
        
        // 检查错误类型
        if nsError.domain == NSURLErrorDomain {
            switch nsError.code {
            case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost:
                loadOfflineErrorPage(title: "无网络连接", message: "请检查您的网络连接后重试")
            case NSURLErrorTimedOut:
                loadOfflineErrorPage(title: "连接超时", message: "服务器响应时间过长，请稍后重试")
            case NSURLErrorCannotFindHost:
                loadOfflineErrorPage(title: "找不到服务器", message: "无法找到该网站，请检查网址是否正确")
            case NSURLErrorBadURL:
                loadOfflineErrorPage(title: "网址格式错误", message: "请检查网址格式是否正确")
            case NSURLErrorCancelled:
                // 用户取消加载，不显示错误
                break
            default:
                loadOfflineErrorPage(title: "加载失败", message: "网页加载遇到问题: \(error.localizedDescription)")
            }
        } else {
            // 其他类型错误
            loadOfflineErrorPage(title: "加载失败", message: error.localizedDescription)
        }
    }
    
    private func loadOfflineErrorPage(title: String, message: String) {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>\(title)</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 20px;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #333;
                }
                .error-container {
                    background: white;
                    border-radius: 16px;
                    padding: 40px;
                    text-align: center;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    max-width: 400px;
                    width: 100%;
                }
                .error-icon {
                    font-size: 72px;
                    margin-bottom: 20px;
                    filter: grayscale(1);
                }
                .error-title {
                    font-size: 24px;
                    font-weight: 600;
                    margin-bottom: 12px;
                    color: #1a1a1a;
                }
                .error-message {
                    font-size: 16px;
                    color: #666;
                    margin-bottom: 30px;
                    line-height: 1.5;
                }
                .retry-button {
                    background: #007AFF;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .retry-button:hover {
                    background: #0056CC;
                    transform: translateY(-1px);
                }
                .retry-button:active {
                    transform: translateY(0);
                }
            </style>
        </head>
        <body>
            <div class="error-container">
                <div class="error-icon">🌐</div>
                <div class="error-title">\(title)</div>
                <div class="error-message">\(message)</div>
                <button class="retry-button" onclick="window.location.reload()">重新加载</button>
            </div>
        </body>
        </html>
        """
        
        if let webView = tab.webView {
            webView.loadHTMLString(errorHTML, baseURL: nil)
        }
    }

    // MARK: - SSL证书验证处理
    func webView(_ webView: WKWebView, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
        // 使用SecurityService处理证书验证
        let (disposition, credential) = browserViewModel.securityService.validateCertificate(challenge)
        completionHandler(disposition, credential)
    }

    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
        guard let url = navigationAction.request.url else {
            print("🛡️ [导航拦截] ❌ 无效URL，取消请求")
            decisionHandler(.cancel)
            return
        }

        let urlString = url.absoluteString
        print("🛡️ [导航拦截] 🔍 检查导航请求: \(urlString)")

        // 1. HTTPS 强制升级
        let finalURL = browserViewModel.enforceHTTPS(for: url)
        if finalURL != url {
            print("🛡️ [导航拦截] 🔒 HTTPS升级: \(url.absoluteString) -> \(finalURL.absoluteString)")
            // 需要重定向到 HTTPS
            let httpsRequest = URLRequest(url: finalURL)
            webView.load(httpsRequest)
            decisionHandler(.cancel)
            return
        }

        // 2. 综合安全检查
        if !browserViewModel.validateURLSecurity(url) {
            print("🛡️ [导航拦截] 🚫 安全检查失败，拦截请求: \(urlString)")
            
            // 显示安全警告页面
            DispatchQueue.main.async {
                self.showSecurityWarning(for: url, in: webView)
            }
            
            decisionHandler(.cancel)
            return
        }

        // 3. 基础安全检查已完成，允许导航
        print("🛡️ [导航拦截] ✅ 允许导航: \(urlString)")
        decisionHandler(.allow)
    }
    
    // MARK: - 安全警告页面
    private func showSecurityWarning(for url: URL, in webView: WKWebView) {
        let warningHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <title>安全警告</title>
            <style>
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #ff4757, #ff3838);
                    color: white;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                }
                .warning-container {
                    background: rgba(255, 255, 255, 0.95);
                    color: #333;
                    padding: 40px;
                    border-radius: 20px;
                    text-align: center;
                    max-width: 500px;
                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                }
                .warning-icon {
                    font-size: 80px;
                    margin-bottom: 20px;
                }
                .warning-title {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 15px;
                    color: #ff4757;
                }
                .warning-message {
                    font-size: 16px;
                    line-height: 1.6;
                    margin-bottom: 30px;
                    color: #666;
                }
                .warning-url {
                    background: #f8f9fa;
                    padding: 12px;
                    border-radius: 8px;
                    font-family: monospace;
                    font-size: 14px;
                    word-break: break-all;
                    margin-bottom: 25px;
                }
                .button-container {
                    display: flex;
                    gap: 15px;
                    justify-content: center;
                }
                .button {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s;
                }
                .back-button {
                    background: #007AFF;
                    color: white;
                }
                .back-button:hover {
                    background: #0056CC;
                }
                .force-button {
                    background: #f8f9fa;
                    color: #666;
                }
                .force-button:hover {
                    background: #e9ecef;
                }
            </style>
        </head>
        <body>
            <div class="warning-container">
                <div class="warning-icon">🛡️</div>
                <div class="warning-title">安全警告</div>
                <div class="warning-message">
                    此网站可能存在安全风险，建议您不要继续访问。我们检测到了潜在的安全威胁，包括：
                    <br><br>
                    • 恶意网站或钓鱼攻击<br>
                    • 不安全的HTTP连接<br>
                    • 可疑的网站行为模式
                </div>
                <div class="warning-url">\(url.absoluteString)</div>
                <div class="button-container">
                    <button class="button back-button" onclick="history.back()">返回安全页面</button>
                    <button class="button force-button" onclick="forceNavigate()">忽略警告继续</button>
                </div>
            </div>
            <script>
                function forceNavigate() {
                    if (confirm('确定要忽略安全警告并继续访问此网站吗？这可能会危及您的数据安全。')) {
                        window.location.href = '\(url.absoluteString)';
                    }
                }
            </script>
        </body>
        </html>
        """
        
        webView.loadHTMLString(warningHTML, baseURL: nil)
    }

    // MARK: - WKUIDelegate
    func webView(_ webView: WKWebView, createWebViewWith configuration: WKWebViewConfiguration, for navigationAction: WKNavigationAction, windowFeatures: WKWindowFeatures) -> WKWebView? {
        // 检查导航类型，确定是否应该在当前页面打开
        if navigationAction.targetFrame == nil {
            // 对于target="_blank"的链接，在当前页面打开
            if let url = navigationAction.request.url {
                webView.load(URLRequest(url: url))
            }
        }
        return nil
    }

    func webView(_ webView: WKWebView, runJavaScriptAlertPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping () -> Void) {
        // 处理 JavaScript 警告框
        DispatchQueue.main.async {
            let alert = UIAlertController(title: "网页消息", message: message, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                completionHandler()
            })

            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                window.rootViewController?.present(alert, animated: true)
            }
        }
    }

    func webView(_ webView: WKWebView, runJavaScriptConfirmPanelWithMessage message: String, initiatedByFrame frame: WKFrameInfo, completionHandler: @escaping (Bool) -> Void) {
        // 处理 JavaScript 确认框
        DispatchQueue.main.async {
            let alert = UIAlertController(title: "网页确认", message: message, preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default) { _ in
                completionHandler(true)
            })
            alert.addAction(UIAlertAction(title: "取消", style: .cancel) { _ in
                completionHandler(false)
            })

            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                window.rootViewController?.present(alert, animated: true)
            }
        }
    }

    func webView(_ webView: WKWebView, contextMenuConfigurationForElement elementInfo: WKContextMenuElementInfo, completionHandler: @escaping (UIContextMenuConfiguration?) -> Void) {
        
        guard let url = elementInfo.linkURL else {
            // 如果没有链接URL，检查是否是图片或其他媒体
            if let imageURL = getImageURL(from: elementInfo) {
                completionHandler(createContextMenu(for: imageURL, type: .image))
            } else {
                completionHandler(nil)
            }
            return
        }
        
        completionHandler(createContextMenu(for: url, type: .link))
    }
    
    // MARK: - 上下文菜单配置
    private func createContextMenu(for url: URL, type: ContextMenuType) -> UIContextMenuConfiguration {
        return UIContextMenuConfiguration(identifier: nil, previewProvider: nil) { _ in
            var actions: [UIAction] = []
            
            // 复制链接
            let copyAction = UIAction(
                title: "复制链接",
                image: UIImage(systemName: "doc.on.doc")
            ) { _ in
                UIPasteboard.general.string = url.absoluteString
            }
            actions.append(copyAction)
            
            // 在新标签页打开
            if type == .link {
                let newTabAction = UIAction(
                    title: "在新标签页打开",
                    image: UIImage(systemName: "plus.square.on.square")
                ) { _ in
                    self.browserViewModel.createNewTab(url: url)
                }
                actions.append(newTabAction)
            }
            
            // 分享链接
            let shareAction = UIAction(
                title: "分享链接",
                image: UIImage(systemName: "square.and.arrow.up"),
                attributes: []
            ) { _ in
                let activityVC = UIActivityViewController(activityItems: [url], applicationActivities: nil)
                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                   let window = windowScene.windows.first,
                   let rootViewController = window.rootViewController {
                    rootViewController.present(activityVC, animated: true)
                }
            }
            actions.append(shareAction)
            
            return UIMenu(title: "", children: actions)
        }
    }
    
    // MARK: - 网页内容处理
    private func handleWebContent(url: URL) {
        // 处理网页内容的基础逻辑
        print("🌐 正在处理网页内容: \(url.absoluteString)")
    }
    
    // MARK: - 获取图片URL
    private func getImageURL(from elementInfo: WKContextMenuElementInfo) -> URL? {
        // 由于WKContextMenuElementInfo的限制，这里需要使用JavaScript来获取图片URL
        // 这是一个简化的实现
        return nil
    }
    
    // MARK: - 显示确认提示
    private func showConfirmation(message: String) {
        // 创建简单的Toast提示
        let alertController = UIAlertController(title: "操作成功", message: message, preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "确定", style: .default))
        
        // 获取当前的视图控制器来显示alert
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first,
           let rootViewController = window.rootViewController {
            rootViewController.present(alertController, animated: true)
        }
    }
    
    // MARK: - 上下文菜单类型
    private enum ContextMenuType {
        case link
        case image
        case text
    }

    // MARK: - WKScriptMessageHandler (空实现 - 视频全屏现在由原生处理器处理)
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        // 处理安全警告页面的消息
        if message.name == "securityWarning" {
            if let messageBody = message.body as? [String: Any],
               let action = messageBody["action"] as? String {
                
                DispatchQueue.main.async {
                    switch action {
                    case "trustAndContinue":
                        if let urlString = messageBody["url"] as? String,
                           let url = URL(string: urlString) {
                            // 将网站添加到用户信任列表
                            self.browserViewModel.addToUserTrustedURLs(url)
                            // 重新加载网站
                            self.browserViewModel.loadURL(url)
                        }
                    case "goHome":
                        // 导航到主页或新标签页
                        self.tab.url = nil
                        self.tab.title = "新标签页"
                        
                    default:
                        break
                    }
                }
            }
            return
        }
        
        // 处理其他原有的消息（如果有的话）
    }
    
    // MARK: - 性能监控
    private func startPerformanceMonitoring() {
        // 每30秒检查一次性能状态
        Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            self?.checkPerformanceStatus()
        }
    }
    
    private func checkPerformanceStatus() {
        guard let webView = observedWebView else { return }
        
        let now = Date()
        _ = now.timeIntervalSince(lastPerformanceCheck) // 使用下划线忽略未使用的变量
        lastPerformanceCheck = now
        
        // 检查内存使用情况
        let memoryInfo = ProcessInfo.processInfo.physicalMemory
        let availableMemory = memoryInfo / (1024 * 1024) // MB
        
        if availableMemory < 512 { // 如果可用内存少于512MB
            print("⚠️ 内存不足警告: \(availableMemory)MB 可用")
            
            // 建议清理WebView缓存
            webView.configuration.websiteDataStore.removeData(
                ofTypes: [WKWebsiteDataTypeDiskCache, WKWebsiteDataTypeMemoryCache],
                modifiedSince: Date(timeIntervalSinceNow: -3600), // 清理1小时前的缓存
                completionHandler: {
                    print("🧹 WebView缓存已清理")
                }
            )
        }
        
        // 重置性能计数器
        if messageQueueWarningCount > 5 {
            print("📊 检测到频繁的消息队列警告，建议刷新页面")
            messageQueueWarningCount = 0
        }
    }
    
    private func logPerformanceWarning() {
        messageQueueWarningCount += 1
        print("⚠️ WebKit性能警告 #\(messageQueueWarningCount): 消息队列积累")
        
        if messageQueueWarningCount >= 3 {
            print("💡 建议: 当前页面可能包含大量动画或JavaScript，考虑刷新页面")
        }
    }
}

// MARK: - 新标签页空状态
struct NewTabEmptyState: View {
    @ObservedObject var browserViewModel: NewWebBrowserViewModel
    let onURLLoad: (URL) -> Void
    @State private var searchText = ""
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            // 主标题区域
            VStack(spacing: 16) {
                Image(systemName: "globe.americas.fill")
                    .font(.system(size: 72, weight: .light))
                    .foregroundStyle(
                        LinearGradient(
                            colors: [.blue, .purple],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                
                VStack(spacing: 8) {
                    Text("新标签页")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text("开始您的浏览之旅")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
            }
            
            // 搜索框
            VStack(spacing: 16) {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                        .font(.system(size: 16))
                    
                    TextField("搜索或输入网址", text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                        .font(.body)
                        .onSubmit {
                            handleSearch()
                        }
                        .focused($isSearchFocused)
                    
                    if !searchText.isEmpty {
                        Button(action: {
                            searchText = ""
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.secondary)
                                .font(.system(size: 16))
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(UIColor.systemGray6))
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSearchFocused ? Color.blue : Color.clear, lineWidth: 2)
                )
                .onTapGesture {
                    // 只有点击搜索框区域才激活
                    isSearchFocused = true
                }
            }
            .padding(.horizontal, 32)
            
            // 快捷操作
            if !browserViewModel.bookmarks.isEmpty {
                VStack(spacing: 16) {
                    Text("快速访问")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 16) {
                            ForEach(Array(browserViewModel.bookmarks.prefix(6)), id: \.id) { bookmark in
                                QuickAccessCard(bookmark: bookmark) {
                                    if let url = URL(string: bookmark.url) {
                                        onURLLoad(url)
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 32)
                    }
                }
            }
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
    
    private func handleSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        if let url = browserViewModel.processURLInput(searchText) {
            onURLLoad(url)
        }
        
        searchText = ""
        isSearchFocused = false
    }
}

// MARK: - 快速访问卡片
struct QuickAccessCard: View {
    let bookmark: BookmarkItem
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                // 网站图标
                AsyncImage(url: faviconURL) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                } placeholder: {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.1))
                        .overlay(
                            Image(systemName: "globe")
                                .foregroundColor(.blue)
                                .font(.title2)
                        )
                }
                .frame(width: 44, height: 44)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                
                // 网站标题
                Text(bookmark.title)
                    .font(.caption)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
            }
            .frame(width: 80)
            .padding(.vertical, 8)
        }
        .buttonStyle(ScaleButtonStyle())
    }
    
    private var faviconURL: URL? {
        guard let url = URL(string: bookmark.url),
              let host = url.host else { return nil }
        return URL(string: "https://www.google.com/s2/favicons?domain=\(host)&sz=64")
    }
}

// MARK: - 地址栏建议行组件
struct AddressSuggestionRow: View {
    let suggestion: AddressSuggestion
    let action: () -> Void
    
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: suggestion.icon)
                    .font(.system(size: 16))
                    .foregroundColor(.secondary)
                    .frame(width: 20, height: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(suggestion.title)
                        .font(.body)
                        .foregroundColor(.primary)
                        .lineLimit(1)
                    
                    Text(suggestion.subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                Image(systemName: "arrow.up.left")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .contentShape(Rectangle())
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 无标签页空状态
struct NoTabsEmptyState: View {
    let onCreateTab: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Spacer()
            
            Image(systemName: "square.on.square.dashed")
                .font(.system(size: 72, weight: .light))
                .foregroundColor(.secondary)
            
            VStack(spacing: 12) {
                Text("没有打开的标签页")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                
                Text("创建一个新标签页开始浏览")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            
            Button(action: onCreateTab) {
                HStack(spacing: 8) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .medium))
                    Text("创建新标签页")
                        .font(.body)
                        .fontWeight(.medium)
                }
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(10)
            }
            .buttonStyle(ScaleButtonStyle())
            
            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
}

// MARK: - 简化的弹窗类型枚举
enum BrowserSheetType: Identifiable {
    case tabSwitcher
    case bookmarks
    
    var id: String {
        switch self {
        case .tabSwitcher: return "tabSwitcher"
        case .bookmarks: return "bookmarks"
        }
    }
}
