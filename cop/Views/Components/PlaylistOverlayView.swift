//
//  PlaylistOverlayView.swift
//  cop
//
//  Created by AI Assistant on 2025/6/15.
//  播放列表覆盖层视图
//

import SwiftUI

// MARK: - 播放列表覆盖层视图
struct PlaylistOverlayView: View {
    @ObservedObject var playlist: VideoPlaylist
    let onItemSelected: (PlaylistItem) -> Void
    let onClose: () -> Void
    
    @State private var showingPlaybackModeMenu = false
    
    var body: some View {
        HStack(spacing: 0) {
            // 半透明背景，点击关闭
            Color.black.opacity(0.3)
                .onTapGesture {
                    onClose()
                }
            
            // 播放列表面板
            VStack(spacing: 0) {
                // 头部
                headerView
                
                // 播放列表内容
                if playlist.items.isEmpty {
                    emptyStateView
                } else {
                    playlistContentView
                }
                
                // 底部控制
                bottomControlsView
            }
            .frame(width: 320)
            .background(Color.black.opacity(0.9))
            .cornerRadius(16, corners: [.topLeft, .bottomLeft])
        }
        .ignoresSafeArea()
    }
    
    // MARK: - 头部视图
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("播放列表")
                    .font(.headline)
                    .foregroundColor(.white)
                
                Text("\(playlist.items.count) 个视频")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
            
            Button(action: onClose) {
                Image(systemName: "xmark")
                    .font(.title2)
                    .foregroundColor(.white)
            }
        }
        .padding()
        .background(Color.black.opacity(0.5))
    }
    
    // MARK: - 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "music.note.list")
                .font(.system(size: 48))
                .foregroundColor(.gray)
            
            Text("播放列表为空")
                .font(.headline)
                .foregroundColor(.white)
            
            Text("添加视频到播放列表")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 播放列表内容视图
    private var playlistContentView: some View {
        ScrollView {
            LazyVStack(spacing: 0) {
                ForEach(Array(playlist.items.enumerated()), id: \.element.id) { index, item in
                    PlaylistItemRow(
                        item: item,
                        isCurrentItem: index == playlist.currentIndex,
                        onTap: {
                            playlist.jumpTo(index: index)
                            onItemSelected(item)
                        },
                        onRemove: {
                            playlist.removeItem(at: index)
                        }
                    )
                    .background(
                        index == playlist.currentIndex ?
                        Color.blue.opacity(0.2) : Color.clear
                    )
                }
            }
        }
    }
    
    // MARK: - 底部控制视图
    private var bottomControlsView: some View {
        VStack(spacing: 12) {
            Divider()
                .background(Color.gray)
            
            // 播放模式控制
            HStack {
                Button(action: {
                    showingPlaybackModeMenu = true
                }) {
                    HStack(spacing: 8) {
                        Image(systemName: playlist.playbackMode.iconName)
                        Text(playlist.playbackMode.displayName)
                    }
                    .font(.caption)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.gray.opacity(0.3))
                    .cornerRadius(16)
                }
                .actionSheet(isPresented: $showingPlaybackModeMenu) {
                    ActionSheet(
                        title: Text("播放模式"),
                        buttons: PlaybackMode.allCases.map { mode in
                            .default(Text(mode.displayName)) {
                                playlist.setPlaybackMode(mode)
                            }
                        } + [.cancel()]
                    )
                }
                
                Spacer()
                
                // 随机播放按钮
                Button(action: {
                    playlist.toggleShuffle()
                }) {
                    Image(systemName: "shuffle")
                        .font(.title3)
                        .foregroundColor(playlist.isShuffled ? .blue : .white)
                }
                
                // 清空列表按钮
                Button(action: {
                    playlist.clearPlaylist()
                }) {
                    Image(systemName: "trash")
                        .font(.title3)
                        .foregroundColor(.red)
                }
            }
            .padding(.horizontal)
            
            // 统计信息
            let stats = playlist.getPlaybackStats()
            HStack {
                Text("总时长: \(stats.totalDuration)")
                Spacer()
                Text("位置: \(stats.currentPosition)")
            }
            .font(.caption2)
            .foregroundColor(.gray)
            .padding(.horizontal)
            .padding(.bottom)
        }
        .background(Color.black.opacity(0.5))
    }
}

// MARK: - 播放列表项目行
struct PlaylistItemRow: View {
    let item: PlaylistItem
    let isCurrentItem: Bool
    let onTap: () -> Void
    let onRemove: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // 播放指示器或序号
            ZStack {
                if isCurrentItem {
                    Image(systemName: "play.fill")
                        .font(.caption)
                        .foregroundColor(.blue)
                } else {
                    Text("\(item.mediaFile.name.prefix(1))")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            .frame(width: 24, height: 24)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(4)
            
            // 视频信息
            VStack(alignment: .leading, spacing: 4) {
                Text(item.title)
                    .font(.body)
                    .foregroundColor(isCurrentItem ? .blue : .white)
                    .lineLimit(2)
                
                HStack {
                    if let duration = item.duration {
                        Text(formatDuration(duration))
                            .font(.caption2)
                            .foregroundColor(.gray)
                    }
                    
                    Spacer()
                    
                    Text(formatFileSize(item.mediaFile.fileSize))
                        .font(.caption2)
                        .foregroundColor(.gray)
                }
            }
            
            Spacer()
            
            // 删除按钮
            Button(action: onRemove) {
                Image(systemName: "minus.circle.fill")
                    .font(.title3)
                    .foregroundColor(.red.opacity(0.7))
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .contentShape(Rectangle())
        .onTapGesture {
            onTap()
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    private func formatFileSize(_ size: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: size)
    }
}

// 注意：cornerRadius扩展已在AppDesignSystem中定义，这里不需要重复声明

// MARK: - 预览
#if DEBUG
struct PlaylistOverlayView_Previews: PreviewProvider {
    static var previews: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            PlaylistOverlayView(
                playlist: {
                    let playlist = VideoPlaylist()
                    // 添加示例数据
                    return playlist
                }(),
                onItemSelected: { _ in },
                onClose: { }
            )
        }
    }
}
#endif
