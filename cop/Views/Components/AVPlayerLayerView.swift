//
//  AVPlayerLayerView.swift
//  cop
//
//  Created by AI Assistant on 2025/6/15.
//  高性能AVPlayerLayer视频播放组件 - 替代SwiftUI VideoPlayer以获得更好性能
//

import SwiftUI
import AVFoundation
import UIKit

// MARK: - AVPlayerLayer包装器
struct AVPlayerLayerView: UIViewRepresentable {
    let player: AVPlayer?
    let videoGravity: AVLayerVideoGravity
    let showsPlaybackControls: Bool
    
    init(
        player: AVPlayer?,
        videoGravity: AVLayerVideoGravity = .resizeAspect,
        showsPlaybackControls: Bool = false
    ) {
        self.player = player
        self.videoGravity = videoGravity
        self.showsPlaybackControls = showsPlaybackControls
    }
    
    func makeUIView(context: Context) -> PlayerLayerView {
        let view = PlayerLayerView()
        view.configure(
            player: player,
            videoGravity: videoGravity,
            showsPlaybackControls: showsPlaybackControls
        )
        return view
    }
    
    func updateUIView(_ uiView: PlayerLayerView, context: Context) {
        uiView.updatePlayer(player)
        uiView.updateVideoGravity(videoGravity)
        uiView.updatePlaybackControls(showsPlaybackControls)
    }
}

// MARK: - 自定义UIView包装AVPlayerLayer
class PlayerLayerView: UIView {
    
    // MARK: - Properties
    private var playerLayer: AVPlayerLayer!
    private var playbackControlsView: PlaybackControlsView?
    private var player: AVPlayer? {
        didSet {
            playerLayer?.player = player
        }
    }
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupPlayerLayer()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupPlayerLayer()
    }
    
    // MARK: - 布局
    override func layoutSubviews() {
        super.layoutSubviews()
        playerLayer.frame = bounds
        playbackControlsView?.frame = bounds
    }
    
    // MARK: - 设置
    private func setupPlayerLayer() {
        playerLayer = AVPlayerLayer()
        playerLayer.videoGravity = .resizeAspect
        playerLayer.backgroundColor = UIColor.black.cgColor
        layer.addSublayer(playerLayer)
    }
    
    // MARK: - 配置方法
    func configure(
        player: AVPlayer?,
        videoGravity: AVLayerVideoGravity,
        showsPlaybackControls: Bool
    ) {
        self.player = player
        playerLayer.videoGravity = videoGravity
        
        if showsPlaybackControls {
            addPlaybackControls()
        } else {
            removePlaybackControls()
        }
    }
    
    func updatePlayer(_ player: AVPlayer?) {
        self.player = player
    }
    
    func updateVideoGravity(_ videoGravity: AVLayerVideoGravity) {
        playerLayer.videoGravity = videoGravity
    }
    
    func updatePlaybackControls(_ showsPlaybackControls: Bool) {
        if showsPlaybackControls {
            addPlaybackControls()
        } else {
            removePlaybackControls()
        }
    }
    
    // MARK: - 播放控制
    private func addPlaybackControls() {
        guard playbackControlsView == nil else { return }
        
        let controlsView = PlaybackControlsView()
        controlsView.frame = bounds
        controlsView.configure(with: player)
        addSubview(controlsView)
        playbackControlsView = controlsView
    }
    
    private func removePlaybackControls() {
        playbackControlsView?.removeFromSuperview()
        playbackControlsView = nil
    }
}

// MARK: - 播放控制视图
class PlaybackControlsView: UIView {
    
    // MARK: - UI Components
    private let playPauseButton = UIButton(type: .system)
    private let progressSlider = UISlider()
    private let currentTimeLabel = UILabel()
    private let durationLabel = UILabel()
    private let controlsStackView = UIStackView()
    
    // MARK: - Properties
    private weak var player: AVPlayer?
    private var timeObserver: Any?
    private var isUserInteracting = false
    
    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    deinit {
        removeTimeObserver()
    }
    
    // MARK: - UI设置
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.3)
        
        // 播放/暂停按钮
        playPauseButton.setImage(UIImage(systemName: "play.fill"), for: .normal)
        playPauseButton.tintColor = .white
        playPauseButton.addTarget(self, action: #selector(playPauseButtonTapped), for: .touchUpInside)
        
        // 进度条
        progressSlider.minimumValue = 0
        progressSlider.maximumValue = 1
        progressSlider.tintColor = .white
        progressSlider.addTarget(self, action: #selector(progressSliderChanged), for: .valueChanged)
        progressSlider.addTarget(self, action: #selector(progressSliderTouchDown), for: .touchDown)
        progressSlider.addTarget(self, action: #selector(progressSliderTouchUp), for: [.touchUpInside, .touchUpOutside])
        
        // 时间标签
        currentTimeLabel.textColor = .white
        currentTimeLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 12, weight: .regular)
        currentTimeLabel.text = "0:00"
        
        durationLabel.textColor = .white
        durationLabel.font = UIFont.monospacedDigitSystemFont(ofSize: 12, weight: .regular)
        durationLabel.text = "0:00"
        
        // 布局
        setupLayout()
    }
    
    private func setupLayout() {
        // 时间和进度条容器
        let timeProgressStack = UIStackView(arrangedSubviews: [currentTimeLabel, progressSlider, durationLabel])
        timeProgressStack.axis = .horizontal
        timeProgressStack.spacing = 8
        timeProgressStack.alignment = .center
        
        // 主控制栈
        controlsStackView.addArrangedSubview(playPauseButton)
        controlsStackView.addArrangedSubview(timeProgressStack)
        controlsStackView.axis = .vertical
        controlsStackView.spacing = 12
        controlsStackView.alignment = .center
        
        addSubview(controlsStackView)
        controlsStackView.translatesAutoresizingMaskIntoConstraints = false
        
        NSLayoutConstraint.activate([
            controlsStackView.centerXAnchor.constraint(equalTo: centerXAnchor),
            controlsStackView.bottomAnchor.constraint(equalTo: safeAreaLayoutGuide.bottomAnchor, constant: -20),
            controlsStackView.leadingAnchor.constraint(greaterThanOrEqualTo: leadingAnchor, constant: 20),
            controlsStackView.trailingAnchor.constraint(lessThanOrEqualTo: trailingAnchor, constant: -20),
            
            timeProgressStack.leadingAnchor.constraint(equalTo: controlsStackView.leadingAnchor),
            timeProgressStack.trailingAnchor.constraint(equalTo: controlsStackView.trailingAnchor),
            
            playPauseButton.widthAnchor.constraint(equalToConstant: 44),
            playPauseButton.heightAnchor.constraint(equalToConstant: 44)
        ])
    }
    
    // MARK: - 配置
    func configure(with player: AVPlayer?) {
        self.player = player
        setupTimeObserver()
        updatePlayPauseButton()
    }
    
    // MARK: - 时间观察器
    private func setupTimeObserver() {
        removeTimeObserver()
        
        guard let player = player else { return }
        
        let timeScale = CMTimeScale(NSEC_PER_SEC)
        let time = CMTime(seconds: 0.1, preferredTimescale: timeScale)
        
        timeObserver = player.addPeriodicTimeObserver(forInterval: time, queue: .main) { [weak self] time in
            self?.updateProgress(time)
        }
    }
    
    private func removeTimeObserver() {
        if let timeObserver = timeObserver {
            player?.removeTimeObserver(timeObserver)
            self.timeObserver = nil
        }
    }
    
    // MARK: - 更新UI
    private func updateProgress(_ time: CMTime) {
        guard !isUserInteracting else { return }
        
        let currentTime = CMTimeGetSeconds(time)
        let duration = player?.currentItem?.duration.seconds ?? 0
        
        if duration.isFinite && duration > 0 {
            progressSlider.value = Float(currentTime / duration)
            durationLabel.text = formatTime(duration)
        }
        
        currentTimeLabel.text = formatTime(currentTime)
    }
    
    private func updatePlayPauseButton() {
        let isPlaying = player?.rate != 0
        let imageName = isPlaying ? "pause.fill" : "play.fill"
        playPauseButton.setImage(UIImage(systemName: imageName), for: .normal)
    }
    
    private func formatTime(_ time: TimeInterval) -> String {
        guard time.isFinite else { return "0:00" }
        
        let minutes = Int(time) / 60
        let seconds = Int(time) % 60
        return String(format: "%d:%02d", minutes, seconds)
    }
    
    // MARK: - 用户交互
    @objc private func playPauseButtonTapped() {
        guard let player = player else { return }
        
        if player.rate == 0 {
            player.play()
        } else {
            player.pause()
        }
        
        updatePlayPauseButton()
    }
    
    @objc private func progressSliderTouchDown() {
        isUserInteracting = true
    }
    
    @objc private func progressSliderChanged() {
        guard let player = player,
              let duration = player.currentItem?.duration.seconds,
              duration.isFinite && duration > 0 else { return }
        
        let targetTime = Double(progressSlider.value) * duration
        currentTimeLabel.text = formatTime(targetTime)
    }
    
    @objc private func progressSliderTouchUp() {
        defer { isUserInteracting = false }
        
        guard let player = player,
              let duration = player.currentItem?.duration.seconds,
              duration.isFinite && duration > 0 else { return }
        
        let targetTime = Double(progressSlider.value) * duration
        let cmTime = CMTime(seconds: targetTime, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        
        player.seek(to: cmTime) { [weak self] completed in
            DispatchQueue.main.async {
                if completed {
                    self?.updatePlayPauseButton()
                }
            }
        }
    }
}
